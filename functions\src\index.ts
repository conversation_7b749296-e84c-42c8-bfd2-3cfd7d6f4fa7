// Firebase Functions for Hive Campus
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import { createHash } from 'crypto';
import cors from 'cors';

// Initialize Firebase Admin
admin.initializeApp();

// CORS configuration for development and production
const corsHandler = cors({
  origin: [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://localhost:5000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});

// Simple test function
export const testFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Test function working',
      timestamp: new Date().toISOString()
    });
  });

// Function to set admin PIN
export const setAdminPin = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify the user is authenticated and is an admin
      if (!context.auth) {
        throw new functions.https.HttpsError(
          'unauthenticated',
          'User must be authenticated'
        );
      }

      const { pin } = data;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'PIN must be exactly 8 digits'
        );
      }

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Only admin users can set PIN'
        );
      }

      // Hash the PIN for security
      const hashedPin = createHash('sha256').update(pin).digest('hex');

      // Store the hashed PIN in admin settings
      await admin.firestore().collection('adminSettings').doc('security').set({
        adminPin: hashedPin,
        pinSetAt: admin.firestore.Timestamp.now(),
        pinSetBy: context.auth.uid
      }, { merge: true });

      console.log(`Admin PIN set by user: ${context.auth.uid}`);

      return {
        success: true,
        message: 'Admin PIN set successfully'
      };

    } catch (error) {
      console.error('Error setting admin PIN:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to set admin PIN',
        error
      );
    }
  });

// Function to verify admin PIN
export const verifyAdminPin = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify the user is authenticated and is an admin
      if (!context.auth) {
        throw new functions.https.HttpsError(
          'unauthenticated',
          'User must be authenticated'
        );
      }

      const { pin } = data;

      if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'PIN must be exactly 8 digits'
        );
      }

      // Verify user is admin
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Only admin users can verify PIN'
        );
      }

      // Get stored PIN hash
      const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
      if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
        throw new functions.https.HttpsError(
          'not-found',
          'Admin PIN not set. Please set up your PIN first.'
        );
      }

      // Hash the provided PIN and compare
      const hashedPin = createHash('sha256').update(pin).digest('hex');
      const storedPin = securityDoc.data()?.adminPin;

      if (hashedPin !== storedPin) {
        throw new functions.https.HttpsError(
          'permission-denied',
          'Invalid PIN'
        );
      }

      // Update last access time
      await admin.firestore().collection('users').doc(context.auth.uid).update({
        lastAdminAccess: admin.firestore.Timestamp.now()
      });

      console.log(`Admin PIN verified for user: ${context.auth.uid}`);

      return {
        success: true,
        message: 'PIN verified successfully'
      };

    } catch (error) {
      console.error('Error verifying admin PIN:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to verify admin PIN',
        error
      );
    }
  });

// Function to fix existing admin user (for setup)
export const fixAdminUser = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, _context) => {
    try {
      const { email } = data;

      if (!email) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Email is required'
        );
      }

      // Get user by email
      const userRecord = await admin.auth().getUserByEmail(email);

      // Set custom claims with both admin and role
      await admin.auth().setCustomUserClaims(userRecord.uid, {
        admin: true,
        role: 'admin'
      });

      // Update user profile in Firestore
      await admin.firestore().collection('users').doc(userRecord.uid).set({
        role: 'admin',
        updatedAt: admin.firestore.Timestamp.now(),
        status: 'active',
        adminLevel: 'super',
        permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
      }, { merge: true });

      console.log(`Admin user fixed: ${email} (${userRecord.uid})`);

      return {
        success: true,
        message: 'Admin user configured successfully',
        uid: userRecord.uid
      };

    } catch (error) {
      console.error('Error fixing admin user:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Failed to fix admin user',
        error
      );
    }
  });

// Get Stripe Connect account status
export const getStripeConnectAccountStatus = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching Stripe Connect account status for user:', userId);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        console.log(`No connect account found for user: ${userId}`);
        return null;
      }

      const connectAccount = connectAccountDoc.data();
      console.log('Connect account data:', connectAccount);

      return {
        accountId: connectAccount?.stripeAccountId || null,
        onboardingUrl: connectAccount?.onboardingUrl || null,
        dashboardUrl: connectAccount?.dashboardUrl || null,
        isOnboarded: connectAccount?.isOnboarded || false,
        chargesEnabled: connectAccount?.chargesEnabled || false,
        payoutsEnabled: connectAccount?.payoutsEnabled || false,
      };
    } catch (error) {
      console.error('Error in getStripeConnectAccountStatus:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get pending payouts for seller
export const getSellerPendingPayouts = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log('Fetching pending payouts for user:', userId);

      // Query orders where the user is the seller and funds haven't been released
      const ordersQuery = admin.firestore()
        .collection('orders')
        .where('sellerId', '==', userId)
        .where('status', '==', 'payment_succeeded')
        .where('fundsReleased', '==', false)
        .orderBy('createdAt', 'desc')
        .limit(50);

      const ordersSnapshot = await ordersQuery.get();
      const pendingPayouts = ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log(`Found ${pendingPayouts.length} pending payouts for user: ${userId}`);
      return pendingPayouts;
    } catch (error) {
      console.error('Error in getSellerPendingPayouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Create Stripe Connect account
export const createStripeConnectAccount = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { accountType } = data;
      const userId = context.auth.uid;

      if (!accountType || !['student', 'merchant'].includes(accountType)) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
      }

      console.log(`Creating Stripe Connect account for user: ${userId}, type: ${accountType}`);

      // Check if user already has a Connect account
      const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
      if (existingAccountDoc.exists) {
        const existingAccount = existingAccountDoc.data();
        console.log('User already has a Connect account:', existingAccount);

        return {
          accountId: existingAccount?.stripeAccountId,
          onboardingUrl: existingAccount?.onboardingUrl || `https://hivecampus.app/settings/payment?existing=true`
        };
      }

      // For now, create a mock account since we don't have Stripe configured
      const mockAccountId = `acct_mock_${userId.substring(0, 8)}`;
      const mockConnectAccount = {
        userId: userId,
        stripeAccountId: mockAccountId,
        accountType: accountType,
        isOnboarded: false,
        chargesEnabled: false,
        payoutsEnabled: false,
        detailsSubmitted: false,
        onboardingUrl: `https://hivecampus.app/settings/payment?mock=true&message=Stripe Connect setup will be available soon`,
        isMockAccount: true,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      };

      await admin.firestore().collection('connectAccounts').doc(userId).set(mockConnectAccount);

      console.log(`Mock Stripe Connect account created for user: ${userId}`);

      return {
        accountId: mockAccountId,
        onboardingUrl: mockConnectAccount.onboardingUrl,
      };
    } catch (error) {
      console.error('Error in createStripeConnectAccount:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Get Stripe Connect onboarding link
export const getStripeConnectOnboardingLink = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const userId = context.auth.uid;
      console.log(`Getting onboarding link for user: ${userId}`);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'No Stripe account found for user');
      }

      const connectAccount = connectAccountDoc.data();

      if (connectAccount?.isOnboarded) {
        throw new functions.https.HttpsError('failed-precondition', 'Account is already fully onboarded');
      }

      // Return the existing onboarding URL or create a new mock one
      const onboardingUrl = connectAccount?.onboardingUrl ||
        `https://hivecampus.app/settings/payment?mock=true&message=Stripe Connect setup will be available soon`;

      return {
        onboardingUrl: onboardingUrl
      };
    } catch (error) {
      console.error('Error in getStripeConnectOnboardingLink:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });
